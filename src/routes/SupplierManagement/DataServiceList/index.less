.g-data-service {
    .u-input-num {
        .ant-input-number-handler-wrap {
            display: none;
        }
    }
}

.page-global-body.data-source {
    min-width: 1124px;

    .page-global-body-queryform {
        display: flex;

        .tnt-queryform {
            width: 100%;
        }
    }

    // 搜索过滤容器
    .search-filter-container {
        display: flex;
    }

    // 卡片式表格样式
    .table-card-body {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .ant-table-thead>tr>th {
            background-color: #fafafa;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 500;
            border-bottom: 1px solid #f0f0f0;
        }

        .table-row-card {
            transition: all 0.2s;

            &:hover {
                background-color: #f5f9ff;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            td {
                background: transparent;
                padding: 16px 8px;
            }
        }

        .ant-table-tbody>tr>td {
            border-bottom: 1px solid #f0f0f0;
        }
    }

    // 查询表单样式优化
    .ant-form.tnt-query-form {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        margin-bottom: 16px;
        padding: 16px;
    }

    // 分页样式优化
    .page-global-body-pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16px;
        padding: 12px 16px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .ant-pagination {
            .ant-pagination-item-active {
                border-color: #1890ff;

                a {
                    color: #1890ff;
                }
            }

            .ant-pagination-options {
                .ant-select-selection {
                    border-radius: 4px;
                }
            }
        }
    }

    // 卡片布局样式
    .card-list-container {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        position: relative;
        min-height: 200px;

        .card-list-container-no-result {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 450px;
        }

        // 加载状态遮罩层
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            backdrop-filter: blur(2px);
            transition: opacity 0.3s ease;

            .loading-content {
                display: flex;
                align-items: center;

                .loading-text {
                    margin-left: 8px;
                    font-size: 14px;
                }
            }
        }

        // 卡片淡入动画
        .service-card {
            animation: fadeInUp 0.3s ease-out;
        }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .service-card {
        width: calc(33.333% - 12px);
        background: radial-gradient(75.65% 68.54% at 14.32% -17.42%, #EDF4FF 0%, #FFF 100%);
        border-radius: 8px;
        padding: 16px;
        transition: all 0.3s ease;
        position: relative;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        gap: 16px;
        overflow: hidden;
        min-height: 180px;

        &:hover {
            background: radial-gradient(75.65% 68.54% at 14.32% -17.42%, #CDE0FF 0%, #FFF 100%);
            box-shadow: 0 0 5px -5px rgba(0, 0, 0, 0.05), 0 0 25px 0 rgba(0, 0, 0, 0.10);

            .card-actions-container {
                transform: translateY(0);
                opacity: 1;
            }

            .create-time-row {
                opacity: 0;
                transform: translateY(100%);
                visibility: hidden;
            }
        }

        .card-actions-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 24px 16px 8px 16px;
            transform: translateY(100%);
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            z-index: 5;

            // 毛玻璃背景效果
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);

            // 添加渐变遮罩，让毛玻璃效果更自然
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.9));
                pointer-events: none;
                z-index: -1;
            }

            .card-action-button {
                height: 30px;
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 4px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                color: #17233D;
                transition: all 0.2s ease;
                padding: 0 12px;
                flex: 1;
                margin: 0 4px;
                white-space: nowrap;
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
                border: 1px solid rgba(255, 255, 255, 0.3);

                &:first-child {
                    margin-left: 0;
                }

                &:last-child {
                    margin-right: 0;
                }

                &:hover {
                    background-color: rgba(255, 255, 255, 1);
                    transform: translateY(-1px);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                }

                &.disabled {
                    background-color: rgba(245, 245, 245, 0.8);
                    color: #999;
                    cursor: not-allowed;

                    &:hover {
                        transform: none;
                        box-shadow: none;
                    }
                }
            }
        }

        .create-time-row {
            transition: all 0.3s ease;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .avatar-container {
                width: 40px;
                height: 40px;
                border-radius: 100px;
                background-color: #7A5AF8;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #fff;
                font-size: 14px;
                flex-shrink: 0;
            }

            .header-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 4px;
                margin-left: 12px;

                .title-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .service-name {
                        font-size: 14px;
                        font-weight: 600;
                        color: #17233D;
                        flex: 1;
                    }

                    .service-status {
                        display: flex;
                        align-items: center;

                        .status-badge {
                            display: flex;
                            align-items: center;
                            cursor: pointer;
                            padding: 4px 8px;
                            border-radius: 4px;
                            transition: background-color 0.2s;

                            &:hover {
                                background-color: rgba(0, 0, 0, 0.05);
                            }

                            .status-dot {
                                width: 8px;
                                height: 8px;
                                border-radius: 50%;

                                &.online {
                                    background-color: #07C790;
                                }

                                &.offline {
                                    background-color: #999;
                                }
                            }

                            .status-text {
                                margin-left: 8px;
                                font-size: 14px;
                                color: #17233D;
                            }
                        }
                    }
                }

                .tag-row {
                    display: flex;
                    gap: 8px;

                    .service-tag {
                        background-color: #F1F2F5;
                        border-radius: 8px;
                        padding: 1px 8px;
                        font-size: 12px;
                        color: #454F64;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 22px;
                    }
                }
            }
        }

        .card-content {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .info-label {
                    color: #17233D;
                    font-size: 14px;
                }

                .info-value {
                    color: #8B919E;
                    font-size: 14px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: right;
                    max-width: 70%;
                }
            }
        }
    }
}

.no-drop-btn {
    cursor: no-drop;
    color: gray;
}

// 状态开关样式
.u-checked {
    &.ant-switch-checked {
        background-color: #52c41a;
    }

    &.ant-switch {
        min-width: 60px;
    }
}

// 三点操作菜单样式
.operation-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 3px;
    position: absolute;
    top: 16px;
    right: 16px;

    &:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .dot {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #999;
        margin: 0 2px;
    }
}

.operation-dropdown-menu {
    overflow: hidden;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.10), 0px 0px 5px -5px rgba(0, 0, 0, 0.05);
    min-width: 201px !important;
    padding: 4px 0 4px 0;

    .ant-dropdown-menu {
        background: transparent;
        box-shadow: none;
        border-radius: 16px;
        padding: 0;
    }

    .ant-dropdown-menu-item {
        padding: 5px 12px;
        font-size: 14px;
        color: #17233D;
        background: #fff;
        border-radius: 0;
        height: 32px;
        line-height: 22px;
        display: flex;
        align-items: center;
        transition: background 0.2s;
        margin: 0;

        &:hover {
            background-color: #f5f5f5;
        }

        &.ant-dropdown-menu-item-disabled {
            color: rgba(0, 0, 0, 0.25);
            cursor: not-allowed;
            background: #fff;
        }
    }

    .ant-dropdown-menu-item-divider,
    .ant-dropdown-menu-divider {
        height: 1px;
        background: #e1e6ee;
        margin: 0 12px 4px;
        border-radius: 2px;
    }
}